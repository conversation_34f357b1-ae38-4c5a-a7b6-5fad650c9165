import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, HomeIcon } from '@heroicons/react/24/outline';
import { useAppStore } from '../../stores/appStore';
import { usePlayerStore } from '../../stores/playerStore';
import type { M3UChannel } from '../../types';
import { ChannelList } from '../../components/ChannelList/ChannelList';
import { Button } from '../../components/ui/Button';
import { ErrorMessage } from '../../components/ui/ErrorMessage';

export const ChannelListPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentPlaylist, error, clearError } = useAppStore();
  const { setCurrentChannel } = usePlayerStore();

  const handleChannelPlay = (channel: M3UChannel) => {
    setCurrentChannel(channel);
    navigate('/player');
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  // 如果沒有播放清單，重定向到首頁
  if (!currentPlaylist) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            沒有載入播放清單
          </h2>
          <p className="text-gray-600 mb-6">
            請先載入一個播放清單來查看頻道
          </p>
          <Button onClick={handleBackToHome} leftIcon={<HomeIcon className="h-5 w-5" />}>
            回到首頁
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* 頁面標題 */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={handleBackToHome}
                leftIcon={<ArrowLeftIcon className="h-5 w-5" />}
              >
                返回
              </Button>

              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {currentPlaylist.name}
                </h1>
                <p className="text-sm text-gray-600">
                  共 {currentPlaylist.totalChannels} 個頻道
                </p>
              </div>
            </div>

            <div className="text-sm text-gray-500">
              最後更新: {new Date(currentPlaylist.lastUpdated).toLocaleString('zh-TW')}
            </div>
          </div>
        </div>

        {/* 錯誤訊息 */}
        {error && (
          <div className="mb-6">
            <ErrorMessage
              message={error}
              onDismiss={clearError}
            />
          </div>
        )}

        {/* 頻道列表 */}
        {currentPlaylist.channels.length > 0 ? (
          <ChannelList
            channels={currentPlaylist.channels}
            onChannelPlay={handleChannelPlay}
          />
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              播放清單為空
            </h3>
            <p className="text-gray-600 mb-6">
              此播放清單中沒有可用的頻道
            </p>
            <Button onClick={handleBackToHome} variant="outline">
              載入其他播放清單
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
