import type { M3UChannel, M3UPlaylist, ApiResponse } from '../types';

export class M3UParser {
  /**
   * 從 URL 獲取並解析 M3U 播放清單
   */
  static async parseFromUrl(url: string): Promise<ApiResponse<M3UPlaylist>> {
    try {
      // 驗證 URL 格式
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: '無效的 URL 格式'
        };
      }

      // 獲取 M3U 內容
      const response = await fetch(url);
      if (!response.ok) {
        return {
          success: false,
          error: `無法獲取播放清單: ${response.status} ${response.statusText}`
        };
      }

      const content = await response.text();
      const playlist = this.parseM3UContent(content, url);

      return {
        success: true,
        data: playlist
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '解析播放清單時發生錯誤'
      };
    }
  }

  /**
   * 解析 M3U 內容
   */
  static parseM3UContent(content: string, sourceUrl: string): M3UPlaylist {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    const channels: M3UChannel[] = [];

    let currentChannel: Partial<M3UChannel> = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.startsWith('#EXTM3U')) {
        // M3U 檔案標頭，跳過
        continue;
      }

      if (line.startsWith('#EXTINF:')) {
        // 解析頻道資訊
        currentChannel = this.parseExtInf(line);
      } else if (line.startsWith('http://') || line.startsWith('https://')) {
        // 頻道 URL
        if (currentChannel.name) {
          const channel: M3UChannel = {
            id: this.generateChannelId(currentChannel.name, line),
            name: currentChannel.name,
            url: line,
            logo: currentChannel.logo,
            group: currentChannel.group,
            tvgId: currentChannel.tvgId,
            tvgName: currentChannel.tvgName,
            resolution: currentChannel.resolution,
            language: currentChannel.language
          };
          channels.push(channel);
        }
        currentChannel = {};
      }
    }

    return {
      id: this.generatePlaylistId(sourceUrl),
      name: this.extractPlaylistName(sourceUrl),
      url: sourceUrl,
      channels,
      lastUpdated: new Date(),
      totalChannels: channels.length
    };
  }

  /**
   * 解析 #EXTINF 行
   */
  private static parseExtInf(line: string): Partial<M3UChannel> {
    const channel: Partial<M3UChannel> = {};

    // 提取頻道名稱（最後的部分）
    const nameMatch = line.match(/,(.+)$/);
    if (nameMatch) {
      channel.name = nameMatch[1].trim();
    }

    // 提取各種屬性
    const attributes = [
      { key: 'tvg-id', prop: 'tvgId' },
      { key: 'tvg-name', prop: 'tvgName' },
      { key: 'tvg-logo', prop: 'logo' },
      { key: 'group-title', prop: 'group' },
      { key: 'resolution', prop: 'resolution' },
      { key: 'language', prop: 'language' }
    ];

    attributes.forEach(({ key, prop }) => {
      const regex = new RegExp(`${key}="([^"]*)"`, 'i');
      const match = line.match(regex);
      if (match) {
        (channel as any)[prop] = match[1];
      }
    });

    return channel;
  }

  /**
   * 驗證 URL 是否有效
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return url.startsWith('http://') || url.startsWith('https://');
    } catch {
      return false;
    }
  }

  /**
   * 生成頻道 ID
   */
  private static generateChannelId(name: string, url: string): string {
    return btoa(`${name}-${url}`).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * 生成播放清單 ID
   */
  private static generatePlaylistId(url: string): string {
    return btoa(url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * 從 URL 提取播放清單名稱
   */
  private static extractPlaylistName(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop() || 'playlist';
      return filename.replace(/\.(m3u|m3u8)$/i, '');
    } catch {
      return 'Unknown Playlist';
    }
  }

  /**
   * 驗證頻道 URL 是否可播放
   */
  static async validateChannelUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }
}
