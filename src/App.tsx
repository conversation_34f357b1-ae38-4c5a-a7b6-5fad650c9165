import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAppStore } from './stores/appStore';
import { Home } from './pages/Home/Home';
import { ChannelListPage } from './pages/ChannelList/ChannelListPage';
import { PlayerPage } from './pages/Player/PlayerPage';
import { SettingsPage } from './pages/Settings/SettingsPage';
import { Navigation } from './components/Navigation/Navigation';

function App() {
  const { initializeApp } = useAppStore();

  useEffect(() => {
    // 初始化應用程式
    initializeApp();
  }, [initializeApp]);

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/channels" element={<ChannelListPage />} />
          <Route path="/player" element={<PlayerPage />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
