// M3U 播放清單相關類型
export interface M3UChannel {
  id: string;
  name: string;
  url: string;
  logo?: string;
  group?: string;
  tvgId?: string;
  tvgName?: string;
  resolution?: string;
  language?: string;
}

export interface M3UPlaylist {
  id: string;
  name: string;
  url: string;
  channels: M3UChannel[];
  lastUpdated: Date;
  totalChannels: number;
}

// 播放器相關類型
export interface PlayerState {
  isPlaying: boolean;
  currentChannel: M3UChannel | null;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  currentTime: number;
  duration: number;
  isLoading: boolean;
  error: string | null;
}

// 應用狀態類型
export interface AppState {
  currentPlaylist: M3UPlaylist | null;
  recentPlaylists: M3UPlaylist[];
  favoriteChannels: M3UChannel[];
  settings: AppSettings;
  isLoading: boolean;
  error: string | null;
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  autoplay: boolean;
  volume: number;
  quality: 'auto' | 'high' | 'medium' | 'low';
  language: string;
}

// QR 掃描相關類型
export interface QRScanResult {
  text: string;
  timestamp: Date;
}

// 本地儲存相關類型
export interface StorageData {
  recentPlaylists: M3UPlaylist[];
  favoriteChannels: M3UChannel[];
  settings: AppSettings;
}

// API 相關類型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// 路由相關類型
export type RouteParams = {
  '/': {};
  '/channels': {};
  '/player': { channelId?: string };
  '/settings': {};
};
