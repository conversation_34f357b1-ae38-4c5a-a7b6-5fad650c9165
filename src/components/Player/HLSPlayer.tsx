import React, { useEffect, useRef } from 'react';
import Hls from 'hls.js';
import { usePlayerStore } from '../../stores/playerStore';
import type { M3UChannel } from '../../types';

interface HLSPlayerProps {
  channel: M3UChannel;
  autoplay?: boolean;
  className?: string;
}

export const HLSPlayer: React.FC<HLSPlayerProps> = ({
  channel,
  autoplay = false,
  className = ''
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const {
    isPlaying,
    volume,
    isMuted,
    setLoading,
    setError,
    clearError,
    setHlsInstance,
    destroyHlsInstance,
    setDuration,
    setCurrentTime,
    play,
    pause
  } = usePlayerStore();

  useEffect(() => {
    const video = videoRef.current;
    if (!video || !channel.url) return;

    setLoading(true);
    clearError();

    // 檢查是否支援 HLS
    if (Hls.isSupported()) {
      const hls = new Hls({
        enableWorker: true,
        lowLatencyMode: true,
        backBufferLength: 90
      });

      // 設定 HLS 實例到 store
      setHlsInstance(hls);

      // 載入影片源
      hls.loadSource(channel.url);
      hls.attachMedia(video);

      // HLS 事件處理
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        setLoading(false);
        if (autoplay) {
          video.play().catch(error => {
            console.error('Autoplay failed:', error);
            setError('自動播放失敗，請手動點擊播放');
          });
        }
      });

      hls.on(Hls.Events.ERROR, (_, data) => {
        console.error('HLS Error:', data);

        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              setError('網路錯誤：無法載入影片串流');
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              setError('媒體錯誤：影片格式不支援或損壞');
              break;
            default:
              setError('播放錯誤：無法播放此頻道');
              break;
          }
          setLoading(false);
        }
      });

      // 清理函數
      return () => {
        hls.destroy();
      };
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari 原生支援 HLS
      video.src = channel.url;
      setLoading(false);

      if (autoplay) {
        video.play().catch(error => {
          console.error('Autoplay failed:', error);
          setError('自動播放失敗，請手動點擊播放');
        });
      }
    } else {
      setError('您的瀏覽器不支援 HLS 播放');
      setLoading(false);
    }

    // 清理函數
    return () => {
      destroyHlsInstance();
    };
  }, [channel.url]);

  // 處理播放/暫停狀態
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.play().catch(error => {
        console.error('Play failed:', error);
        setError('播放失敗');
      });
    } else {
      video.pause();
    }
  }, [isPlaying]);

  // 處理音量變化
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    video.volume = isMuted ? 0 : volume;
    video.muted = isMuted;
  }, [volume, isMuted]);

  // 影片事件處理
  const handleLoadedMetadata = () => {
    const video = videoRef.current;
    if (video) {
      setDuration(video.duration);
    }
  };

  const handleTimeUpdate = () => {
    const video = videoRef.current;
    if (video) {
      setCurrentTime(video.currentTime);
    }
  };

  const handlePlay = () => {
    play();
  };

  const handlePause = () => {
    pause();
  };

  const handleError = () => {
    setError('影片載入失敗');
    setLoading(false);
  };

  const handleWaiting = () => {
    setLoading(true);
  };

  const handleCanPlay = () => {
    setLoading(false);
  };

  return (
    <video
      ref={videoRef}
      className={`w-full h-full bg-black ${className}`}
      controls
      playsInline
      onLoadedMetadata={handleLoadedMetadata}
      onTimeUpdate={handleTimeUpdate}
      onPlay={handlePlay}
      onPause={handlePause}
      onError={handleError}
      onWaiting={handleWaiting}
      onCanPlay={handleCanPlay}
      poster={channel.logo}
    >
      您的瀏覽器不支援影片播放。
    </video>
  );
};
